version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"      # HTTP port for ACME challenge
      - "443:443"    # HTTPS port
      - "8201:8201"  # Direct API access
      - "8202:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./acme.json:/acme/acme.json
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
    networks:
      - traefik
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # HIGH-PRIORITY WEBHOOK INSTANCES (50% of total - Proportional 5:3:2)
  # Routes: /reply_generate, /sociar_webhook, /whatsapp_webhook
  app-webhooks:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend-webhooks
    restart: unless-stopped
    deploy:
      replicas: 3  # Default: local profile (overridden by profile files)
    # extra_hosts:
    # - minio.nextai.asia:*************
    labels:
      - "traefik.enable=true"

      # PRIORITY 1: High-priority webhook routes (HTTPS)
      - "traefik.http.routers.webhooks-secure.rule=Host(`${DOMAIN}`) && (PathPrefix(`/reply_generate`) || PathPrefix(`/sociar_webhook`) || PathPrefix(`/whatsapp_webhook`))"
      - "traefik.http.routers.webhooks-secure.entrypoints=websecure"
      - "traefik.http.routers.webhooks-secure.tls=true"
      - "traefik.http.routers.webhooks-secure.tls.certresolver=myresolver"
      - "traefik.http.routers.webhooks-secure.service=app-webhooks"
      - "traefik.http.routers.webhooks-secure.priority=100"

      # PRIORITY 1: High-priority webhook routes (Direct port 8201)
      - "traefik.http.routers.webhooks-api.rule=PathPrefix(`/reply_generate`) || PathPrefix(`/sociar_webhook`) || PathPrefix(`/whatsapp_webhook`)"
      - "traefik.http.routers.webhooks-api.entrypoints=api"
      - "traefik.http.routers.webhooks-api.service=app-webhooks"
      - "traefik.http.routers.webhooks-api.priority=100"

      # Webhook service configuration
      - "traefik.http.services.app-webhooks.loadbalancer.server.port=8000"
      - "traefik.http.services.app-webhooks.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app-webhooks.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.app-webhooks.loadbalancer.healthcheck.timeout=10s"

  # DOCUMENT PROCESSING INSTANCES (30% of total - Proportional 5:3:2)
  # Routes: /setup_files, /process-documents, /add-documents, /check_status
  app-documents:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend-documents
    restart: unless-stopped
    deploy:
      replicas: 1  # Default: local profile (overridden by profile files)
    # extra_hosts:
    #   - minio.nextai.asia:*************
    labels:
      - "traefik.enable=true"

      # PRIORITY 2: Document processing routes (HTTPS) with session affinity
      - "traefik.http.routers.documents-secure.rule=Host(`${DOMAIN}`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`) || PathPrefix(`/check_status`))"
      - "traefik.http.routers.documents-secure.entrypoints=websecure"
      - "traefik.http.routers.documents-secure.tls=true"
      - "traefik.http.routers.documents-secure.tls.certresolver=myresolver"
      - "traefik.http.routers.documents-secure.service=app-documents"
      - "traefik.http.routers.documents-secure.middlewares=websocket-headers"
      - "traefik.http.routers.documents-secure.priority=50"

      # PRIORITY 2: Document processing routes (Direct port 8201) with session affinity
      - "traefik.http.routers.documents-api.rule=PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`) || PathPrefix(`/check_status`)"
      - "traefik.http.routers.documents-api.entrypoints=api"
      - "traefik.http.routers.documents-api.service=app-documents"
      - "traefik.http.routers.documents-api.middlewares=websocket-headers"
      - "traefik.http.routers.documents-api.priority=50"

      # Document service with token-based session affinity
      - "traefik.http.services.app-documents.loadbalancer.server.port=8000"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.name=doc-session"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.secure=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.httpOnly=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.sameSite=lax"
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.timeout=10s"

      # WebSocket-specific middleware
      - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.X-Real-IP="
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Origin=*"
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Methods=GET,POST,OPTIONS,HEAD"
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Headers=*"

  # GENERAL API INSTANCES (20% of total - Proportional 5:3:2)
  app:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend-general
    restart: unless-stopped
    deploy:
      replicas: 1  # Default: local profile (overridden by profile files)
    # extra_hosts:
    #   - minio.nextai.asia:*************
    labels:
      - "traefik.enable=true"

      # HTTP redirect to HTTPS
      - "traefik.http.routers.app-redirect.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-redirect.entrypoints=web"
      - "traefik.http.routers.app-redirect.middlewares=https-redirect"
      - "traefik.http.routers.app-redirect.priority=1"

      # PRIORITY 3: General API routes (HTTPS) - catch all remaining routes
      - "traefik.http.routers.app-secure.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-secure.entrypoints=websecure"
      - "traefik.http.routers.app-secure.tls=true"
      - "traefik.http.routers.app-secure.tls.certresolver=myresolver"
      - "traefik.http.routers.app-secure.service=app"
      - "traefik.http.routers.app-secure.priority=1"

      # PRIORITY 3: General API routes (Direct port 8201) - catch all remaining routes
      - "traefik.http.routers.app-api.rule=PathPrefix(`/`)"
      - "traefik.http.routers.app-api.entrypoints=api"
      - "traefik.http.routers.app-api.service=app"
      - "traefik.http.routers.app-api.priority=1"

      # General API service configuration
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      - "traefik.http.services.app.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.app.loadbalancer.healthcheck.timeout=10s"

      # HTTPS redirect middleware
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"



networks:
  traefik:
    driver: bridge
