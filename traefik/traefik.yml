# Traefik static configuration for WebSocket support
global:
  sendAnonymousUsage: false

# API and dashboard configuration
api:
  dashboard: true
  insecure: true

# ServersTransport for WebSocket support
serversTransport:
  insecureSkipVerify: true

# Entry points
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"
    http:
      tls:
        options: default
  api:
    address: ":8201"

# Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true
    # Enable swarm mode if using Docker Swarm
    swarmMode: false



# TLS configuration
tls:
  certificates:
    - certFile: /certs/fullchain.pem
      keyFile: /certs/privkey.pem
  options:
    default:
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"

# Logging
log:
  level: INFO

accessLog:
  format: json
  fields:
    names:
      ClientUsername: drop
    headers:
      names:
        Authorization: drop
        Content-Type: keep

# Metrics
metrics:
  prometheus: {}

# Health check
ping: {}
